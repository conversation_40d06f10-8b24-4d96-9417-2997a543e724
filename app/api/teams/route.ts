import { NextRequest, NextResponse } from 'next/server';
import { auth, currentUser } from '@clerk/nextjs/server';
import { db } from '@/lib/db';
import { teams, teamMembers, users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

// GET /api/teams - Get all teams for the current user
export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth();

    if (userId) {
      // User is authenticated - get their teams
      console.log("🔐 Authenticated user - getting user's teams");

      const dbUser = await db.query.users.findFirst({
        where: eq(users.clerkId, userId),
      });

      if (dbUser) {
        // Get the user's team memberships
        const teamMemberships = await db.query.teamMembers.findMany({
          where: eq(teamMembers.userId, dbUser.id),
          with: {
            team: true,
          },
        });

        const userTeams = teamMemberships.map((membership) => membership.team);

        if (userTeams.length > 0) {
          return NextResponse.json({ teams: userTeams });
        }
      }
    }

    // Fallback: return all teams (for testing or if user has no teams)
    console.log("🔧 [FALLBACK] Returning all teams");
    const allTeams = await db.query.teams.findMany({
      orderBy: (team) => [team.name],
    });

    return NextResponse.json({ teams: allTeams });
  } catch (error) {
    console.error("[TEAMS_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// POST /api/teams - Create a new team
export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    const { name } = body;

    if (!name) {
      return new NextResponse("Name is required", { status: 400 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Create the team
    const newTeam = await db.insert(teams).values({
      name: name.trim(),
    }).returning();

    // Add the user as the team owner
    await db.insert(teamMembers).values({
      userId: dbUser.id,
      teamId: newTeam[0].id,
      role: 'owner',
    });

    return NextResponse.json({ team: newTeam[0] });
  } catch (error) {
    console.error("[TEAMS_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
