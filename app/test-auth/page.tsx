'use client';

import { useA<PERSON>, useUser, SignIn<PERSON><PERSON><PERSON>, SignOutButton } from '@clerk/nextjs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

export default function TestAuthPage() {
  const { isLoaded, isSignedIn, userId, sessionId } = useAuth();
  const { user } = useUser();

  if (!isLoaded) {
    return <div className="p-8">Loading...</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Authentication Test</h1>
      
      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Auth Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p><strong>Is Loaded:</strong> {isLoaded ? 'Yes' : 'No'}</p>
            <p><strong>Is Signed In:</strong> {isSignedIn ? 'Yes' : 'No'}</p>
            <p><strong>User ID:</strong> {userId || 'None'}</p>
            <p><strong>Session ID:</strong> {sessionId || 'None'}</p>
          </CardContent>
        </Card>

        {isSignedIn && user && (
          <Card>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <p><strong>Email:</strong> {user.emailAddresses?.[0]?.emailAddress || 'None'}</p>
              <p><strong>First Name:</strong> {user.firstName || 'None'}</p>
              <p><strong>Last Name:</strong> {user.lastName || 'None'}</p>
              <p><strong>Created At:</strong> {user.createdAt ? new Date(user.createdAt).toLocaleString() : 'None'}</p>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {isSignedIn ? (
              <div className="space-y-2">
                <SignOutButton>
                  <Button variant="destructive">Sign Out</Button>
                </SignOutButton>
                <div>
                  <Link href="/dashboard">
                    <Button>Go to Dashboard</Button>
                  </Link>
                </div>
                <div>
                  <Link href="/dashboard/projects">
                    <Button variant="outline">Test Projects Page</Button>
                  </Link>
                </div>
              </div>
            ) : (
              <SignInButton mode="redirect">
                <Button>Sign In</Button>
              </SignInButton>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>API Test</CardTitle>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={async () => {
                try {
                  const response = await fetch('/api/organizations');
                  const data = await response.json();
                  console.log('Organizations API response:', data);
                  alert(`Organizations: ${JSON.stringify(data, null, 2)}`);
                } catch (error) {
                  console.error('API Error:', error);
                  alert(`Error: ${error}`);
                }
              }}
            >
              Test Organizations API
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
