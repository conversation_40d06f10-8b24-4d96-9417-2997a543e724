import { db } from '../lib/db/drizzle';

async function checkData() {
  console.log('🔍 Checking database data...\n');

  try {
    // Check users
    const users = await db.query.users.findMany();
    console.log('👥 Users:');
    users.forEach(user => {
      console.log(`  - ${user.email} (${user.name}) - Role: ${user.role}, Company: ${user.companyName}, ClerkId: ${user.clerkId}`);
    });
    console.log(`Total users: ${users.length}\n`);

    // Check organizations
    const organizations = await db.query.organization.findMany();
    console.log('🏢 Organizations:');
    organizations.forEach(org => {
      console.log(`  - ${org.name} (ID: ${org.id})`);
    });
    console.log(`Total organizations: ${organizations.length}\n`);

    // Check teams
    const teams = await db.query.teams.findMany();
    console.log('👥 Teams:');
    teams.forEach(team => {
      console.log(`  - ${team.name} (ID: ${team.id})`);
    });
    console.log(`Total teams: ${teams.length}\n`);

    // Check team members
    const teamMembers = await db.query.teamMembers.findMany({
      with: {
        user: true,
        team: true,
      },
    });
    console.log('🤝 Team Memberships:');
    teamMembers.forEach(member => {
      console.log(`  - ${member.user.email} is ${member.role} of "${member.team.name}"`);
    });
    console.log(`Total memberships: ${teamMembers.length}\n`);

    // Check projects
    const projects = await db.query.projects.findMany({
      with: {
        organization: true,
      },
    });
    console.log('📋 Projects:');
    projects.forEach(project => {
      console.log(`  - ${project.name} (Org: ${project.organization?.name || 'None'})`);
    });
    console.log(`Total projects: ${projects.length}\n`);

    // Check cranes
    const cranes = await db.query.cranes.findMany({
      with: {
        team: true,
      },
    });
    console.log('🏗️ Cranes:');
    cranes.forEach(crane => {
      console.log(`  - ${crane.name} (Team: ${crane.team?.name || 'None'})`);
    });
    console.log(`Total cranes: ${cranes.length}\n`);

  } catch (error) {
    console.error('❌ Error checking data:', error);
  }
}

checkData()
  .then(() => {
    console.log('✅ Data check completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Data check failed:', error);
    process.exit(1);
  });
