import { NextRequest, NextResponse } from 'next/server';
import { auth, currentUser } from '@clerk/nextjs/server';
import { db } from '@/lib/db';
import { organization, users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

// GET /api/organizations - Get all organizations for the current user
export async function GET(req: NextRequest) {
  console.log("🏢 Organizations API called");

  try {
    const { userId } = await auth();
    console.log(`🔐 Auth result - userId: ${userId ? 'present' : 'missing'}`);

    if (userId) {
      // User is authenticated - get their organization
      console.log("🔐 Authenticated user - getting user's organization");

      const dbUser = await db.query.users.findFirst({
        where: eq(users.clerkId, userId),
      });

      console.log(`👤 DB User found: ${dbUser ? 'yes' : 'no'}`);
      if (dbUser) {
        console.log(`👤 User company: ${dbUser.companyName}`);
      }

      if (dbUser && dbUser.companyName) {
        // Find organization by company name (case-insensitive)
        const allOrgs = await db.query.organization.findMany();
        console.log(`🏢 Total organizations in DB: ${allOrgs.length}`);

        const userOrg = allOrgs.find(org =>
          org.name.toLowerCase() === dbUser.companyName.toLowerCase()
        );

        if (userOrg) {
          console.log(`✅ Found user's organization: ${userOrg.name}`);
          return NextResponse.json({ organizations: [userOrg] });
        } else {
          console.log(`⚠️ No organization found for company: ${dbUser.companyName}`);
          console.log(`Available orgs: ${allOrgs.map(o => o.name).join(', ')}`);
        }
      }
    }

    // Fallback: return all organizations (for testing or if user has no specific org)
    console.log("🔧 [FALLBACK] Returning all organizations");
    const organizations = await db.query.organization.findMany({
      orderBy: (org) => [org.name],
    });

    console.log(`📋 Returning ${organizations.length} organizations`);
    return NextResponse.json({ organizations });
  } catch (error) {
    console.error("[ORGANIZATIONS_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// POST /api/organizations - Create a new organization
export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const body = await req.json();
    const { name, description } = body;

    if (!name) {
      return new NextResponse("Name is required", { status: 400 });
    }

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Create the organization
    const newOrganization = await db.insert(organization).values({
      name: name.trim(),
      description: description?.trim() || null,
    }).returning();

    return NextResponse.json({ organization: newOrganization[0] });
  } catch (error) {
    console.error("[ORGANIZATIONS_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
