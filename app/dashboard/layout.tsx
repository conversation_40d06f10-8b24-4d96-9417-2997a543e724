import { ReactNode } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import UserProfile from './user-profile';
import { Toaster } from '@/components/ui/sonner';

// Navigation items based on user roles
const navigationItems = [
  { name: 'Dashboard', href: '/dashboard', icon: '🏠', roles: ['admin', 'manager', 'operator', 'subcontractor'] },
  { name: 'Projects', href: '/dashboard/projects', icon: '📋', roles: ['admin', 'manager', 'operator', 'subcontractor'] },
  { name: 'Crane<PERSON>', href: '/dashboard/cranes', icon: '🏗️', roles: ['admin', 'manager', 'operator'] },
  { name: 'Schedule', href: '/dashboard/schedule', icon: '📅', roles: ['admin', 'manager', 'operator', 'subcontractor'] },
  { name: 'Team', href: '/dashboard/team', icon: '👥', roles: ['admin'] },
  { name: 'Analytics', href: '/dashboard/analytics', icon: '📊', roles: ['admin', 'manager'] },
  { name: 'Settings', href: '/dashboard/settings', icon: '⚙️', roles: ['admin', 'manager', 'operator', 'subcontractor'] },
];

export default async function DashboardLayout({
  children,
}: {
  children: ReactNode;
}) {
  const user = await currentUser();
  
  // If no user is logged in, redirect to sign-in
  if (!user) {
    redirect('/sign-in');
  }

  // Get user role from public metadata
  const userRole = (user.publicMetadata.role as string) || 'member';

  // Filter navigation items based on user role
  const filteredNavItems = navigationItems.filter(item => 
    item.roles.includes(userRole)
  );

  return (
    <div className="flex min-h-screen flex-col bg-gray-50">
      <header className="flex h-16 items-center justify-between border-b bg-white px-4 md:px-6">
        <Link href="/" className="flex items-center gap-2">
          <Image
            src="/logo.svg"
            alt="LiftrUP Logo"
            width={32}
            height={32}
            className="h-8 w-8"
          />
          <span className="text-xl font-bold">LiftrUP</span>
        </Link>
        
        <div className="flex items-center gap-4">
          <UserProfile />
        </div>
      </header>
      
      <div className="flex flex-1 flex-col md:flex-row">
        {/* Sidebar Navigation */}
        <aside className="w-full border-b bg-white md:w-64 md:border-b-0 md:border-r">
          <nav className="p-4">
            <ul className="space-y-2">
              {filteredNavItems.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="flex items-center rounded-md px-3 py-2 text-gray-700 hover:bg-gray-100"
                  >
                    <span className="mr-3 text-lg">{item.icon}</span>
                    {item.name}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </aside>
        
        {/* Main Content */}
        <main className="flex-1 p-4 md:p-6">{children}</main>
      </div>
      <Toaster />
    </div>
  );
}
