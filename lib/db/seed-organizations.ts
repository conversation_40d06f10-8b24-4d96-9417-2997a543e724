import { db } from './drizzle';
import { organization, teams } from './schema';
import { randomUUID } from 'crypto';

async function seedOrganizations() {
  console.log('🌱 Seeding organizations and teams...');

  try {
    // Create sample organizations
    const organizations = await db.insert(organization).values([
      {
        id: randomUUID(),
        name: 'LiftrUP Construction',
      },
      {
        id: randomUUID(),
        name: 'Metro Building Corp',
      },
      {
        id: randomUUID(),
        name: 'Skyline Developers',
      },
    ]).returning();

    console.log(`✅ Created ${organizations.length} organizations`);

    // Create sample teams
    const createdTeams = await db.insert(teams).values([
      {
        name: 'Tower Crane Team',
      },
      {
        name: 'Mobile Crane Team',
      },
      {
        name: 'Maintenance Team',
      },
      {
        name: 'Site Operations',
      },
    ]).returning();

    console.log(`✅ Created ${createdTeams.length} teams`);

    console.log('🎉 Seeding completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Sign up at http://localhost:3001/sign-up');
    console.log('2. Complete the onboarding process');
    console.log('3. Start creating projects and cranes!');

  } catch (error) {
    console.error('❌ Error seeding data:', error);
    throw error;
  }
}

seedOrganizations()
  .catch((error) => {
    console.error('Seed process failed:', error);
    process.exit(1);
  })
  .finally(() => {
    console.log('Seed process finished. Exiting...');
    process.exit(0);
  });
