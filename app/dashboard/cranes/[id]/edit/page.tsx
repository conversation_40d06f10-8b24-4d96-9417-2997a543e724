'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Save, Loader2, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

interface CraneFormData {
  name: string;
  model: string;
  type: string;
  capacity: string;
  height: string;
  reach: string;
  status: string;
  lastMaintenanceDate: string;
  nextMaintenanceDate: string;
}

const craneTypes = [
  { value: 'tower', label: 'Tower Crane' },
  { value: 'mobile', label: 'Mobile Crane' },
  { value: 'crawler', label: 'Crawler Crane' },
  { value: 'overhead', label: 'Overhead Crane' },
  { value: 'gantry', label: 'Gantry Crane' },
];

const statusOptions = [
  { value: 'available', label: 'Available' },
  { value: 'in-use', label: 'In Use' },
  { value: 'maintenance', label: 'Under Maintenance' },
  { value: 'out-of-service', label: 'Out of Service' },
];

// Fetch crane from API
async function getCrane(id: string) {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/cranes/${id}`, {
      cache: 'no-store',
    });

    if (!response.ok) {
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching crane:', error);
    return null;
  }
}

export default function EditCranePage({ 
  params 
}: { 
  params: { id: string } 
}) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [crane, setCrane] = useState<any>(null);
  const [formData, setFormData] = useState<CraneFormData>({
    name: '',
    model: '',
    type: '',
    capacity: '',
    height: '',
    reach: '',
    status: 'available',
    lastMaintenanceDate: '',
    nextMaintenanceDate: '',
  });

  useEffect(() => {
    async function loadCrane() {
      const craneData = await getCrane(params.id);
      if (craneData) {
        setCrane(craneData);
        setFormData({
          name: craneData.name,
          model: craneData.model || '',
          type: craneData.type,
          capacity: craneData.capacity || '',
          height: craneData.height || '',
          reach: craneData.reach || '',
          status: craneData.status,
          lastMaintenanceDate: craneData.lastMaintenanceDate || '',
          nextMaintenanceDate: craneData.nextMaintenanceDate || '',
        });
      }
    }
    
    loadCrane();
  }, [params.id]);

  const handleInputChange = (field: keyof CraneFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Validate form data
      if (!formData.name.trim()) {
        toast.error('Crane name is required');
        return;
      }

      if (!formData.type) {
        toast.error('Crane type is required');
        return;
      }

      // Validate numeric fields
      if (formData.capacity && isNaN(Number(formData.capacity))) {
        toast.error('Capacity must be a valid number');
        return;
      }

      if (formData.height && isNaN(Number(formData.height))) {
        toast.error('Height must be a valid number');
        return;
      }

      if (formData.reach && isNaN(Number(formData.reach))) {
        toast.error('Reach must be a valid number');
        return;
      }

      // Prepare data for API
      const craneData = {
        name: formData.name.trim(),
        model: formData.model.trim() || null,
        type: formData.type,
        capacity: formData.capacity ? Number(formData.capacity) : null,
        height: formData.height ? Number(formData.height) : null,
        reach: formData.reach ? Number(formData.reach) : null,
        status: formData.status,
        lastMaintenanceDate: formData.lastMaintenanceDate || null,
        nextMaintenanceDate: formData.nextMaintenanceDate || null,
      };

      const response = await fetch(`/api/cranes/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(craneData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to update crane');
      }

      toast.success('Crane updated successfully!');
      router.push(`/dashboard/cranes/${params.id}`);
    } catch (error) {
      console.error('Error updating crane:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update crane. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this crane? This action cannot be undone.')) {
      return;
    }

    setIsDeleting(true);

    try {
      const response = await fetch(`/api/cranes/${params.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to delete crane');
      }

      toast.success('Crane deleted successfully!');
      router.push('/dashboard/cranes');
    } catch (error) {
      console.error('Error deleting crane:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete crane. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  if (!crane) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href={`/dashboard/cranes/${params.id}`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Crane
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Edit Crane</h1>
          <p className="text-gray-600">
            Update crane details and settings
          </p>
        </div>
      </div>

      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle>Crane Details</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Crane Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="e.g., Tower Crane TC-001"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="model">Model</Label>
                <Input
                  id="model"
                  value={formData.model}
                  onChange={(e) => handleInputChange('model', e.target.value)}
                  placeholder="e.g., Liebherr 280 EC-H"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Crane Type *</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => handleInputChange('type', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select crane type" />
                </SelectTrigger>
                <SelectContent>
                  {craneTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="capacity">Capacity (tonnes)</Label>
                <Input
                  id="capacity"
                  type="number"
                  step="0.1"
                  value={formData.capacity}
                  onChange={(e) => handleInputChange('capacity', e.target.value)}
                  placeholder="e.g., 12.0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="height">Height (meters)</Label>
                <Input
                  id="height"
                  type="number"
                  step="0.1"
                  value={formData.height}
                  onChange={(e) => handleInputChange('height', e.target.value)}
                  placeholder="e.g., 60.0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="reach">Reach (meters)</Label>
                <Input
                  id="reach"
                  type="number"
                  step="0.1"
                  value={formData.reach}
                  onChange={(e) => handleInputChange('reach', e.target.value)}
                  placeholder="e.g., 70.0"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="lastMaintenanceDate">Last Maintenance Date</Label>
                <Input
                  id="lastMaintenanceDate"
                  type="date"
                  value={formData.lastMaintenanceDate}
                  onChange={(e) => handleInputChange('lastMaintenanceDate', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="nextMaintenanceDate">Next Maintenance Date</Label>
                <Input
                  id="nextMaintenanceDate"
                  type="date"
                  value={formData.nextMaintenanceDate}
                  onChange={(e) => handleInputChange('nextMaintenanceDate', e.target.value)}
                />
              </div>
            </div>

            <div className="flex justify-between pt-4">
              <Button
                type="button"
                variant="destructive"
                onClick={handleDelete}
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Crane
                  </>
                )}
              </Button>

              <div className="flex gap-4">
                <Link href={`/dashboard/cranes/${params.id}`}>
                  <Button variant="outline" type="button">
                    Cancel
                  </Button>
                </Link>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
