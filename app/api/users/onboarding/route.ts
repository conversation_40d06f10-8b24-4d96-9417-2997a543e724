import { NextResponse } from 'next/server';
import { auth, currentUser } from '@clerk/nextjs/server';
import { clerkClient } from '@clerk/nextjs/server';
import { db } from '@/lib/db';
import { users, organization, teams, teamMembers } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { randomUUID } from 'crypto';

export async function POST(request: Request) {
  console.log('🚀 Onboarding API called');
  try {
    const session = await auth();
    const userId = session?.userId;
    const user = await currentUser();

    console.log('🔐 Auth check:', { userId: !!userId, user: !!user });

    // Check if user is authenticated
    if (!userId || !user) {
      console.log('❌ User not authenticated');
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse request body
    const body = await request.json();
    const { role, companyName } = body;

    console.log('📝 Request body:', { role, companyName });

    // Validate required fields
    if (!role) {
      console.log('❌ Role is required');
      return new NextResponse(JSON.stringify({ error: 'Role is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate role is one of the allowed values
    const allowedRoles = ['admin', 'manager', 'operator', 'subcontractor'];
    if (!allowedRoles.includes(role)) {
      return new NextResponse(JSON.stringify({ error: 'Invalid role' }), {
        status: 400,
      });
    }
    
    // Update user metadata in Clerk
    // TODO: Fix clerkClient import issue
    console.log('⚠️ Skipping Clerk metadata update for now');
    // if (userId) {
    //   await clerkClient.users.updateUserMetadata(userId, {
    //     publicMetadata: {
    //       role,
    //       companyName,
    //       onboardedAt: new Date().toISOString(),
    //     },
    //   });
    // }
    
    // Check if user already exists in our database (by clerkId or email)
    const existingUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    // Also check by email in case the user was created via webhook
    const existingUserByEmail = await db.query.users.findFirst({
      where: eq(users.email, user.emailAddresses[0].emailAddress),
    });

    const userToUpdate = existingUser || existingUserByEmail;
    let dbUser;

    if (userToUpdate) {
      // Update existing user
      console.log('📝 Updating existing user');
      await db.update(users)
        .set({
          clerkId: userId, // Ensure clerkId is updated
          role,
          companyName,
          updatedAt: new Date(),
        })
        .where(eq(users.id, userToUpdate.id));
      dbUser = userToUpdate;
    } else {
      // Create new user
      console.log('📝 Creating new user');
      const newUsers = await db.insert(users).values({
        clerkId: userId,
        email: user.emailAddresses[0].emailAddress,
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim(),
        companyName,
        role,
        createdAt: new Date(),
        updatedAt: new Date(),
      }).returning();
      dbUser = newUsers[0];
    }

    // Create organization if companyName is provided and doesn't exist
    if (companyName && companyName.trim()) {
      console.log('🏢 Creating organization:', companyName);

      // Check if organization already exists for this company name
      const existingOrg = await db.query.organization.findFirst({
        where: eq(organization.name, companyName.trim()),
      });

      if (!existingOrg) {
        // Create new organization
        const newOrg = await db.insert(organization).values({
          id: randomUUID(),
          name: companyName.trim(),
        }).returning();

        console.log('✅ Organization created:', newOrg[0].name);

        // Create a default team for the organization
        const defaultTeam = await db.insert(teams).values({
          name: `${companyName.trim()} Team`,
        }).returning();

        // Add the user as the team owner
        await db.insert(teamMembers).values({
          userId: dbUser.id,
          teamId: defaultTeam[0].id,
          role: 'owner',
        });

        console.log('✅ Default team created and user added as owner');
      } else {
        console.log('🏢 Organization already exists:', existingOrg.name);
      }
    }

    console.log('✅ Onboarding completed successfully');
    return new NextResponse(JSON.stringify({
      success: true,
      message: 'Onboarding completed successfully',
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('❌ Onboarding error:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
