import { redirect } from 'next/navigation';
import { auth } from '@clerk/nextjs/server';
import { currentUser } from '@clerk/nextjs/server';
import OnboardingForm from './onboarding-form';

export default async function OnboardingPage() {
  const session = await auth();
  const userId = session?.userId;
  const user = await currentUser();
  
  // If no user is logged in, redirect to sign-in
  if (!userId || !user) {
    redirect('/sign-in');
  }

  // Check if user has already completed onboarding (this would be stored in your database)
  // For now, we'll just check if they have a role set in public metadata
  const userRole = user.publicMetadata.role;
  
  // If user has already completed onboarding, redirect to dashboard
  if (userRole) {
    redirect('/dashboard');
  }

  // Extract only the data needed by the client component
  const userData = {
    firstName: user.firstName,
    emailAddress: user.emailAddresses[0]?.emailAddress,
  };

  return (
    <div className="container mx-auto max-w-3xl px-4 py-12">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold">Complete Your Profile</h1>
        <p className="mt-2 text-gray-600">
          Tell us a bit more about yourself so we can customize your experience
        </p>
      </div>

      <OnboardingForm userData={userData} />
    </div>
  );
}
