'use client';

import { useUser, useAuth, SignInButton, SignOutButton } from '@clerk/nextjs';

export default function ClerkTestPage() {
  const { isLoaded, isSignedIn, user } = useUser();
  const { userId } = useAuth();

  if (!isLoaded) {
    return <div>Loading...</div>;
  }

  return (
    <div className="p-8 space-y-4">
      <h1 className="text-2xl font-bold">Clerk Test Page</h1>
      
      <div className="space-y-2">
        <p><strong>Is Loaded:</strong> {isLoaded ? 'Yes' : 'No'}</p>
        <p><strong>Is Signed In:</strong> {isSignedIn ? 'Yes' : 'No'}</p>
        <p><strong>User ID:</strong> {userId || 'None'}</p>
        <p><strong>User Email:</strong> {user?.emailAddresses?.[0]?.emailAddress || 'None'}</p>
        <p><strong>User Name:</strong> {user?.firstName} {user?.lastName}</p>
      </div>

      <div className="space-x-4">
        {isSignedIn ? (
          <SignOutButton>
            <button className="bg-red-600 text-white px-4 py-2 rounded">
              Sign Out
            </button>
          </SignOutButton>
        ) : (
          <SignInButton>
            <button className="bg-blue-600 text-white px-4 py-2 rounded">
              Sign In
            </button>
          </SignInButton>
        )}
      </div>
    </div>
  );
}
