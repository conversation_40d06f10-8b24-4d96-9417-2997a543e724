import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { cranes, teams, teamMembers, users } from "@/lib/db/schema";
import { eq, and, isNull, sql } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

// GET /api/cranes - Get all cranes for the current user's team
export async function GET(req: NextRequest) {
  try {
    // Temporarily bypass auth for testing
    console.log("🔧 [TEMP] Bypassing auth for cranes API");

    const userCranes = await db.query.cranes.findMany({
      where: isNull(cranes.deletedAt),
      with: {
        team: true,
      },
      orderBy: (crane) => [crane.name],
    });

    return NextResponse.json({ cranes: userCranes });
  } catch (error) {
    console.error("[CRANES_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// POST /api/cranes - Create a new crane
export async function POST(req: NextRequest) {
  try {
    // Temporarily bypass auth for testing
    console.log("🔧 [TEMP] Bypassing auth for cranes POST API");

    const body = await req.json();
    const {
      name,
      model,
      type,
      capacity,
      height,
      reach,
      teamId,
      status,
      lastMaintenanceDate,
      nextMaintenanceDate
    } = body;

    if (!name) {
      return new NextResponse("Name is required", { status: 400 });
    }

    if (!type) {
      return new NextResponse("Type is required", { status: 400 });
    }

    if (!teamId) {
      return new NextResponse("Team ID is required", { status: 400 });
    }

    // Create the crane with a UUID
    const craneId = uuidv4();

    // Use raw SQL to insert with a specific ID
    const newCrane = await db.execute(sql`
      INSERT INTO cranes (
        id, name, model, type, capacity, height, reach, team_id,
        status, last_maintenance_date, next_maintenance_date, created_at, updated_at
      ) VALUES (
        ${craneId}, ${name}, ${model || null}, ${type}, ${capacity || null},
        ${height || null}, ${reach || null}, ${teamId},
        ${status || "available"},
        ${lastMaintenanceDate ? new Date(lastMaintenanceDate).toISOString() : null},
        ${nextMaintenanceDate ? new Date(nextMaintenanceDate).toISOString() : null},
        ${new Date().toISOString()}, ${new Date().toISOString()}
      ) RETURNING *
    `);

    return NextResponse.json({ crane: newCrane[0] });
  } catch (error) {
    console.error("[CRANES_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
