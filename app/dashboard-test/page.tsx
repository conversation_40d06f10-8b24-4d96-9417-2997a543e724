import { currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  FolderOpen,
  Truck,
  Calendar,
  Users,
  BarChart3,
  Settings,
  Plus,
  Activity
} from 'lucide-react';

export default async function DashboardTestPage() {
  const user = await currentUser();
  
  if (!user) {
    redirect('/sign-in');
  }

  // Get user info
  const role = (user.publicMetadata.role as string) || 'member';
  const companyName = user.publicMetadata.companyName as string;
  const isOnboarded = !!user.publicMetadata.onboardedAt;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <img src="/logo.svg" alt="LiftrUP" className="h-8 w-8" />
              <span className="ml-2 text-xl font-bold text-gray-900">LiftrUP</span>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Welcome, {user.firstName || user.emailAddresses[0]?.emailAddress}
              </span>
              <div className="h-8 w-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                {user.firstName?.[0] || user.emailAddresses[0]?.emailAddress[0] || 'U'}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Welcome Section */}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome to LiftrUP Dashboard
            </h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage your construction projects and crane scheduling
            </p>
          </div>

          {/* User Status */}
          <Card>
            <CardHeader>
              <CardTitle>User Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p><strong>Name:</strong> {user.firstName} {user.lastName}</p>
                <p><strong>Email:</strong> {user.emailAddresses[0]?.emailAddress}</p>
                <p><strong>Role:</strong> {role}</p>
                <p><strong>Company:</strong> {companyName || 'Not set'}</p>
                <p><strong>Onboarded:</strong> {isOnboarded ? 'Yes' : 'No'}</p>
              </div>
              
              {!isOnboarded && (
                <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                  <p className="text-yellow-800">
                    You haven't completed onboarding yet. 
                    <Link href="/onboarding" className="ml-1 text-yellow-900 underline">
                      Complete onboarding
                    </Link>
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Dashboard Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <FolderOpen className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Projects</p>
                    <p className="text-2xl font-bold text-gray-900">12</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Truck className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Cranes</p>
                    <p className="text-2xl font-bold text-gray-900">8</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Calendar className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Bookings</p>
                    <p className="text-2xl font-bold text-gray-900">24</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Activity className="h-8 w-8 text-orange-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Active</p>
                    <p className="text-2xl font-bold text-gray-900">5</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Plus className="h-5 w-5 mr-2" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link href="/dashboard/projects/new" className="block w-full bg-blue-600 text-white text-center py-2 px-4 rounded hover:bg-blue-700">
                  New Project
                </Link>
                <Link href="/dashboard/cranes/new" className="block w-full bg-green-600 text-white text-center py-2 px-4 rounded hover:bg-green-700">
                  Add Crane
                </Link>
                <Link href="/dashboard/schedule" className="block w-full bg-purple-600 text-white text-center py-2 px-4 rounded hover:bg-purple-700">
                  Schedule Booking
                </Link>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span>Project "Downtown Office" created</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                    <span>Crane TC-001 scheduled</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                    <span>Booking confirmed for Site A</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>System Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">All Systems</span>
                    <span className="text-green-600 text-sm font-medium">Operational</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Database</span>
                    <span className="text-green-600 text-sm font-medium">Connected</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">API</span>
                    <span className="text-green-600 text-sm font-medium">Online</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Navigation Links */}
          <div className="text-center space-x-4">
            <Link href="/dashboard" className="text-blue-600 hover:underline">
              Try Real Dashboard
            </Link>
            <Link href="/debug-user" className="text-gray-600 hover:underline">
              Debug User Info
            </Link>
            <Link href="/onboarding" className="text-green-600 hover:underline">
              Complete Onboarding
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
}
