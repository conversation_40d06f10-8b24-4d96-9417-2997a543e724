import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { projects, organization, users, activityLogs, ActivityType } from "@/lib/db/schema";
import { eq, and, isNull, sql } from "drizzle-orm";

// GET /api/projects/[projectId] - Get a specific project by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { projectId: string } }
) {
  try {
    // Temporarily bypass auth for testing
    console.log("🔧 [TEMP] Bypassing auth for project GET API");

    const projectId = params.projectId;

    // Get the project
    const project = await db.query.projects.findFirst({
      where: and(eq(projects.id, projectId), isNull(projects.deletedAt)),
      with: {
        organization: true,
      },
    });

    if (!project) {
      return new NextResponse("Project not found", { status: 404 });
    }

    return NextResponse.json(project);
  } catch (error) {
    console.error("[PROJECT_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// PUT /api/projects/[projectId] - Update a specific project
export async function PUT(
  req: NextRequest,
  { params }: { params: { projectId: string } }
) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const projectId = params.projectId;

    const body = await req.json();
    const { name, description, location, startDate, endDate, status } = body;

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Get the project
    const project = await db.query.projects.findFirst({
      where: and(eq(projects.id, projectId), isNull(projects.deletedAt)),
    });

    if (!project) {
      return new NextResponse("Project not found", { status: 404 });
    }

    // In a real implementation, you would check if the user has permission to update this project
    // For now, we'll assume the user has access to all projects

    // Update the project using raw SQL to handle date conversions properly
    const updatedProject = await db.execute(sql`
      UPDATE projects
      SET 
        name = ${name || project.name},
        description = ${description !== undefined ? description : project.description},
        location = ${location ? JSON.stringify(location) : project.location},
        start_date = ${startDate ? new Date(startDate).toISOString() : project.startDate},
        end_date = ${endDate ? new Date(endDate).toISOString() : project.endDate},
        status = ${status || project.status},
        updated_at = ${new Date().toISOString()}
      WHERE id = ${projectId} AND deleted_at IS NULL
      RETURNING *
    `);

    // Use raw SQL to insert activity log since we need to convert organizationId to integer
    await db.execute(sql`
      INSERT INTO activity_logs (team_id, user_id, action, ip_address)
      VALUES (
        (SELECT id FROM teams LIMIT 1), -- Use the first team as a fallback
        ${dbUser.id},
        ${ActivityType.UPDATE_PROJECT},
        ${req.headers.get("x-forwarded-for") || null}
      )
    `);

    return NextResponse.json(updatedProject[0]);
  } catch (error) {
    console.error("[PROJECT_UPDATE]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// DELETE /api/projects/[projectId] - Soft delete a specific project
export async function DELETE(
  req: NextRequest,
  { params }: { params: { projectId: string } }
) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const projectId = params.projectId;

    // Get the user from the database
    const dbUser = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!dbUser) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Get the project
    const project = await db.query.projects.findFirst({
      where: and(eq(projects.id, projectId), isNull(projects.deletedAt)),
    });

    if (!project) {
      return new NextResponse("Project not found", { status: 404 });
    }

    // In a real implementation, you would check if the user has permission to delete this project
    // For now, we'll assume the user has access to all projects

    // Soft delete the project using raw SQL
    const deletedProject = await db.execute(sql`
      UPDATE projects
      SET deleted_at = ${new Date().toISOString()}
      WHERE id = ${projectId} AND deleted_at IS NULL
      RETURNING *
    `);

    // Use raw SQL to insert activity log since we need to convert organizationId to integer
    await db.execute(sql`
      INSERT INTO activity_logs (team_id, user_id, action, ip_address)
      VALUES (
        (SELECT id FROM teams LIMIT 1), -- Use the first team as a fallback
        ${dbUser.id},
        ${ActivityType.DELETE_PROJECT},
        ${req.headers.get("x-forwarded-for") || null}
      )
    `);

    return NextResponse.json(deletedProject[0]);
  } catch (error) {
    console.error("[PROJECT_DELETE]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
