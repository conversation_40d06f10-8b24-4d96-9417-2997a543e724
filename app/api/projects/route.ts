import { NextRequest, NextResponse } from "next/server";
import { auth, currentUser } from "@clerk/nextjs/server";
import { db } from "@/lib/db";
import { projects, organization, users } from "@/lib/db/schema";
import { eq, and, isNull, sql } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";

// GET /api/projects - Get all projects for the current user's team
export async function GET(req: NextRequest) {
  try {
    // Temporarily bypass auth for testing
    console.log("🔧 [TEMP] Bypassing auth for projects API");

    const userProjects = await db.query.projects.findMany({
      where: isNull(projects.deletedAt),
      with: {
        organization: true,
      },
      orderBy: (project) => [project.createdAt],
    });

    return NextResponse.json({ projects: userProjects });
  } catch (error) {
    console.error("[PROJECTS_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// POST /api/projects - Create a new project
export async function POST(req: NextRequest) {
  try {
    // Temporarily bypass auth for testing
    console.log("🔧 [TEMP] Bypassing auth for projects POST API");

    const body = await req.json();
    const { name, description, organizationId, location, startDate, endDate, status } = body;

    if (!name) {
      return new NextResponse("Name is required", { status: 400 });
    }

    if (!organizationId) {
      return new NextResponse("Organization ID is required", { status: 400 });
    }

    // Verify the organization exists
    const org = await db.query.organization.findFirst({
      where: eq(organization.id, organizationId),
    });

    if (!org) {
      return new NextResponse("Organization not found", { status: 404 });
    }

    // Create the project with a UUID
    const projectId = uuidv4();

    // Use raw SQL to insert with a specific ID (using mock user ID for testing)
    const newProject = await db.execute(sql`
      INSERT INTO projects (
        id, name, description, organization_id, created_by,
        location, safety_compliance, start_date, end_date,
        status, created_at, updated_at
      ) VALUES (
        ${projectId}, ${name}, ${description || null}, ${organizationId}, ${"1"},
        ${location ? JSON.stringify(location) : null}, ${null},
        ${startDate ? new Date(startDate).toISOString() : null}, ${endDate ? new Date(endDate).toISOString() : null},
        ${status || "active"}, ${new Date().toISOString()}, ${new Date().toISOString()}
      ) RETURNING *
    `);

    return NextResponse.json({ project: newProject[0] });
  } catch (error) {
    console.error("[PROJECTS_POST]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}
