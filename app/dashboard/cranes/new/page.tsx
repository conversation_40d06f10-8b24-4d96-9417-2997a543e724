'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Save, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface CraneFormData {
  name: string;
  model: string;
  type: string;
  capacity: string;
  height: string;
  reach: string;
  status: string;
  lastMaintenanceDate: string;
  nextMaintenanceDate: string;
  teamId: string;
}

interface Team {
  id: number;
  name: string;
}

const craneTypes = [
  { value: 'tower', label: 'Tower Crane' },
  { value: 'mobile', label: 'Mobile Crane' },
  { value: 'crawler', label: 'Crawler Crane' },
  { value: 'overhead', label: 'Overhead Crane' },
  { value: 'gantry', label: 'Gantry Crane' },
];

const statusOptions = [
  { value: 'available', label: 'Available' },
  { value: 'in-use', label: 'In Use' },
  { value: 'maintenance', label: 'Under Maintenance' },
  { value: 'out-of-service', label: 'Out of Service' },
];

export default function NewCranePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [teams, setTeams] = useState<Team[]>([]);
  const [loadingTeams, setLoadingTeams] = useState(true);
  const [formData, setFormData] = useState<CraneFormData>({
    name: '',
    model: '',
    type: '',
    capacity: '',
    height: '',
    reach: '',
    status: 'available',
    lastMaintenanceDate: '',
    nextMaintenanceDate: '',
    teamId: '',
  });

  // Load teams on component mount
  useEffect(() => {
    async function loadTeams() {
      try {
        const response = await fetch('/api/teams');
        if (response.ok) {
          const data = await response.json();
          setTeams(data.teams || []);
          // Auto-select first team if only one exists
          if (data.teams?.length === 1) {
            setFormData(prev => ({
              ...prev,
              teamId: data.teams[0].id.toString()
            }));
          }
        }
      } catch (error) {
        console.error('Error loading teams:', error);
        toast.error("Failed to load teams");
      } finally {
        setLoadingTeams(false);
      }
    }

    loadTeams();
  }, []);

  const handleInputChange = (field: keyof CraneFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Validate form data
      if (!formData.name.trim()) {
        toast.error('Crane name is required');
        return;
      }

      if (!formData.type) {
        toast.error('Crane type is required');
        return;
      }

      if (!formData.teamId) {
        toast.error('Please select a team');
        return;
      }

      // Validate numeric fields
      if (formData.capacity && isNaN(Number(formData.capacity))) {
        toast.error('Capacity must be a valid number');
        return;
      }

      if (formData.height && isNaN(Number(formData.height))) {
        toast.error('Height must be a valid number');
        return;
      }

      if (formData.reach && isNaN(Number(formData.reach))) {
        toast.error('Reach must be a valid number');
        return;
      }

      // Prepare data for API
      const craneData = {
        name: formData.name.trim(),
        model: formData.model.trim() || null,
        type: formData.type,
        capacity: formData.capacity ? Number(formData.capacity) : null,
        height: formData.height ? Number(formData.height) : null,
        reach: formData.reach ? Number(formData.reach) : null,
        teamId: Number(formData.teamId),
        status: formData.status,
        lastMaintenanceDate: formData.lastMaintenanceDate || null,
        nextMaintenanceDate: formData.nextMaintenanceDate || null,
      };

      const response = await fetch('/api/cranes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(craneData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to create crane');
      }

      const result = await response.json();
      const newCrane = result.crane || result;

      toast.success('Crane added successfully!');
      router.push(`/dashboard/cranes/${newCrane.id}`);
    } catch (error) {
      console.error('Error creating crane:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to add crane. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/dashboard/cranes">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Fleet
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Add New Crane</h1>
          <p className="text-gray-600">
            Add a new crane to your fleet
          </p>
        </div>
      </div>

      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle>Crane Details</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Crane Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="e.g., Tower Crane TC-001"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="model">Model</Label>
                <Input
                  id="model"
                  value={formData.model}
                  onChange={(e) => handleInputChange('model', e.target.value)}
                  placeholder="e.g., Liebherr 280 EC-H"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="type">Crane Type *</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => handleInputChange('type', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select crane type" />
                  </SelectTrigger>
                  <SelectContent>
                    {craneTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="teamId">Team *</Label>
                <Select
                  value={formData.teamId}
                  onValueChange={(value) => handleInputChange('teamId', value)}
                  disabled={loadingTeams}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={loadingTeams ? "Loading teams..." : "Select a team"} />
                  </SelectTrigger>
                  <SelectContent>
                    {teams.map((team) => (
                      <SelectItem key={team.id} value={team.id.toString()}>
                        {team.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="capacity">Capacity (tonnes)</Label>
                <Input
                  id="capacity"
                  type="number"
                  step="0.1"
                  value={formData.capacity}
                  onChange={(e) => handleInputChange('capacity', e.target.value)}
                  placeholder="e.g., 12.0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="height">Height (meters)</Label>
                <Input
                  id="height"
                  type="number"
                  step="0.1"
                  value={formData.height}
                  onChange={(e) => handleInputChange('height', e.target.value)}
                  placeholder="e.g., 60.0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="reach">Reach (meters)</Label>
                <Input
                  id="reach"
                  type="number"
                  step="0.1"
                  value={formData.reach}
                  onChange={(e) => handleInputChange('reach', e.target.value)}
                  placeholder="e.g., 70.0"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="lastMaintenanceDate">Last Maintenance Date</Label>
                <Input
                  id="lastMaintenanceDate"
                  type="date"
                  value={formData.lastMaintenanceDate}
                  onChange={(e) => handleInputChange('lastMaintenanceDate', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="nextMaintenanceDate">Next Maintenance Date</Label>
                <Input
                  id="nextMaintenanceDate"
                  type="date"
                  value={formData.nextMaintenanceDate}
                  onChange={(e) => handleInputChange('nextMaintenanceDate', e.target.value)}
                />
              </div>
            </div>

            <div className="flex gap-4 pt-4">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Add Crane
                  </>
                )}
              </Button>
              <Link href="/dashboard/cranes">
                <Button variant="outline" type="button">
                  Cancel
                </Button>
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
